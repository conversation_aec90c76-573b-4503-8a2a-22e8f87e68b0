#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化管理器
统一管理所有可视化对象和渲染操作
"""

import vtk
import hashlib
from typing import Dict, List, Optional, Tuple, Any, Callable
from data_models import RegionData
from data_manager import DataManager
from vtk_renderer import VT<PERSON><PERSON><PERSON><PERSON>
from pressure_visualizer import PressureVisualizer
from streamline_visualizer import StreamlineVisualizer
from mesh_visualizer import MeshVisualizer
from surface_field_visualizer import SurfaceFieldVisualizer


class VisualizationManager(VTKRenderer):
    """可视化管理器 - 增强版本，支持智能缓存和状态管理"""

    def __init__(self, data_manager: DataManager):
        super().__init__()
        self.data_manager = data_manager

        # 可视化器
        self.pressure_visualizer = PressureVisualizer()
        self.streamline_visualizer = StreamlineVisualizer()
        self.mesh_visualizer = MeshVisualizer()
        self.surface_field_visualizer = SurfaceFieldVisualizer()

        # 颜色条actors
        self.velocity_color_bar = None

        # 可视化对象缓存
        self.region_vtk_grids: Dict[str, vtk.vtkUnstructuredGrid] = {}
        # 核心物理场actors（预创建）
        self.region_actors: Dict[str, Dict[str, vtk.vtkActor]] = {}
        # 扩展物理场actors（延迟创建）
        self.extended_field_actors: Dict[str, Dict[str, vtk.vtkActor]] = {}
        # 网格actors
        self.mesh_actors: Dict[str, vtk.vtkActor] = {}

        # 支持的物理场定义
        self.core_fields = ['pressure', 'contour', 'streamline', 'vector']  # 核心字段，预创建
        self.extended_fields = ['temperature', 'density', 'velocity_magnitude',
                              'turbulence_kinetic_energy', 'turbulence_dissipation']  # 扩展字段，延迟创建

        # 状态管理
        self._mesh_data_hash: Optional[str] = None
        self._flow_data_hash: Optional[str] = None
        self._last_selected_regions: List[str] = []
        self._mesh_actors_created: bool = False
        self._flow_actors_created: bool = False

        # 全局颜色映射范围
        self._global_scalar_range: Optional[Tuple[float, float]] = None

        # 可视化参数
        self.sampling_rate = 1
        self.streamline_density = 50
        self.vector_scale = 1.0
        self.contour_count = 15
        self.contour_line_width = 2
        
    def setup_visualizers(self):
        """设置所有可视化器的渲染组件"""
        self.pressure_visualizer.setup_renderer(self.renderer, self.render_window, self.interactor)
        self.streamline_visualizer.setup_renderer(self.renderer, self.render_window, self.interactor)
        self.mesh_visualizer.setup_renderer(self.renderer, self.render_window, self.interactor)

    def _calculate_data_hash(self, data_source: str, selected_regions: List[str]) -> str:
        """计算数据哈希值，用于检测数据变更"""
        import hashlib

        # 组合数据源、区域列表和参数
        hash_data = f"{data_source}:{','.join(sorted(selected_regions))}:{self.sampling_rate}"

        # 添加流体数据的时间戳或版本信息
        if self.data_manager.has_flow_data(data_source):
            flow_data = self.data_manager.get_flow_data_regions(data_source)
            if flow_data:
                # 使用数据的基本信息作为哈希输入
                for region_name in selected_regions:
                    if region_name in flow_data:
                        region_data = flow_data[region_name]
                        hash_data += f":{region_name}:{len(region_data)}"

        return hashlib.md5(hash_data.encode()).hexdigest()

    def _calculate_mesh_hash(self, selected_regions: List[str]) -> str:
        """计算网格数据哈希值"""
        import hashlib

        hash_data = f"mesh:{','.join(sorted(selected_regions))}"

        # 添加网格数据信息
        for region_name in selected_regions:
            mesh_region = self.data_manager.get_mesh_region(region_name)
            if mesh_region:
                hash_data += f":{region_name}:{len(mesh_region.points) if hasattr(mesh_region, 'points') else 0}"

        return hashlib.md5(hash_data.encode()).hexdigest()

    def _need_recreate_flow_actors(self, data_source: str, selected_regions: List[str]) -> bool:
        """检查是否需要重新创建流体可视化actors"""
        current_hash = self._calculate_data_hash(data_source, selected_regions)

        if (self._flow_data_hash != current_hash or
            not self._flow_actors_created or
            not self.region_vtk_grids):
            self._flow_data_hash = current_hash
            return True
        return False

    def _need_recreate_mesh_actors(self, selected_regions: List[str]) -> bool:
        """检查是否需要重新创建网格actors"""
        current_hash = self._calculate_mesh_hash(selected_regions)

        if (self._mesh_data_hash != current_hash or
            not self._mesh_actors_created or
            not self.mesh_actors):
            self._mesh_data_hash = current_hash
            return True
        return False
        
    def create_visualization(self, data_source: str, selected_regions: List[str],
                           show_pressure: bool = True, show_contours: bool = False,
                           show_streamlines: bool = True, show_vectors: bool = False,
                           show_mesh: bool = False, use_optimized_streamlines: bool = True,
                           extended_fields: Optional[Dict[str, bool]] = None,
                           progress_callback: Optional[Callable] = None) -> bool:
        """
        创建完整的可视化

        Args:
            data_source: 数据源 ("steady" 或 "transient")
            selected_regions: 选中的区域列表
            show_pressure: 是否显示压力分布
            show_contours: 是否显示压力轮廓
            show_streamlines: 是否显示流线
            show_vectors: 是否显示速度矢量
            show_mesh: 是否显示网格
            extended_fields: 扩展物理场显示选项 {'temperature': True, 'density': False, ...}
            progress_callback: 进度回调函数

        Returns:
            bool: 是否创建成功
        """
        def check_cancelled():
            """检查是否被取消"""
            if progress_callback and hasattr(progress_callback, 'is_cancelled'):
                return progress_callback.is_cancelled()
            return False

        try:
            if progress_callback:
                progress_callback(10, "正在准备数据...")
                if check_cancelled():
                    return False

            # 检查数据
            if not self.data_manager.has_flow_data(data_source) and not show_mesh:
                print("没有流体数据且未选择显示网格")
                return False

            if not selected_regions:
                print("没有选择区域")
                return False

            # 清空现有可视化
            self.clear_visualization()

            if progress_callback:
                progress_callback(20, "正在创建VTK网格...")
                if check_cancelled():
                    return False

            # 创建VTK网格
            success = self._create_vtk_grids(data_source, selected_regions, progress_callback)
            if not success and not show_mesh:
                return False

            if progress_callback:
                progress_callback(60, "正在创建核心可视化对象...")
                if check_cancelled():
                    return False

            # 创建所有核心可视化对象（预创建模式）
            self._create_all_core_actors(selected_regions, progress_callback, data_source, use_optimized_streamlines)

            if progress_callback:
                progress_callback(75, "正在创建扩展物理场...")
                if check_cancelled():
                    return False

            # 创建扩展物理场（延迟创建模式）
            if extended_fields:
                self._create_extended_field_actors(selected_regions, extended_fields, progress_callback)

            if progress_callback:
                progress_callback(85, "正在创建网格...")
                if check_cancelled():
                    return False

            # 创建网格可视化
            self._create_mesh_actors(selected_regions)

            if progress_callback:
                progress_callback(90, "正在设置可见性...")
                if check_cancelled():
                    return False

            # 设置可见性
            self._update_all_visibility(selected_regions, show_pressure, show_contours,
                                      show_streamlines, show_vectors, show_mesh, extended_fields)

            print(f"可见性设置完成 - 压力: {show_pressure}, 轮廓: {show_contours}, 流线: {show_streamlines}, 矢量: {show_vectors}")

            if progress_callback:
                progress_callback(95, "正在添加辅助元素...")
                if check_cancelled():
                    return False

            # 添加坐标轴和颜色条
            has_scalar_field = (show_pressure or show_contours or
                              (extended_fields and any(extended_fields.values())))
            self._add_auxiliary_elements(has_scalar_field)

            # 设置相机和渲染
            self.reset_camera()
            self.render()

            if progress_callback:
                progress_callback(100, "可视化创建完成")

            print(f"可视化创建完成: {len(selected_regions)} 个区域")
            return True

        except Exception as e:
            print(f"创建可视化失败: {e}")
            return False
            
    def update_visualization_display(self, selected_regions: List[str],
                                   show_pressure: bool = True, show_contours: bool = False,
                                   show_streamlines: bool = True, show_vectors: bool = False,
                                   show_mesh: bool = False, extended_fields: Optional[Dict[str, bool]] = None):
        """更新可视化显示（使用缓存的对象）"""
        try:
            # 更新核心物理场可见性
            self._update_region_actors_visibility(selected_regions, show_pressure, show_contours,
                                                show_streamlines, show_vectors)

            # 更新扩展物理场可见性
            if extended_fields:
                self._update_extended_field_visibility(selected_regions, extended_fields)

            # 更新网格可见性
            self._update_mesh_visibility(selected_regions, show_mesh)

            # 更新颜色条显示（根据当前可见性状态）
            has_scalar_field = (show_pressure or show_contours or
                              (extended_fields and any(extended_fields.values())))
            self._update_color_bars()

            # 重新渲染
            self.render()

            print(f"可视化显示已更新: {len(selected_regions)} 个区域")

        except Exception as e:
            print(f"更新可视化显示失败: {e}")
            
    def clear_visualization(self):
        """清空所有可视化"""
        if self.renderer:
            self.remove_all_actors()
        # 清空缓存
        self.region_actors.clear()
        self.extended_field_actors.clear()
        self.mesh_actors.clear()
        self.region_vtk_grids.clear()
            
    def _create_vtk_grids(self, data_source: str, selected_regions: List[str], 
                         progress_callback: Optional[Callable] = None) -> bool:
        """创建VTK网格"""
        try:
            self.region_vtk_grids.clear()
            
            flow_data = self.data_manager.get_flow_data_regions(data_source)
            if not flow_data:
                return False
                
            total_regions = len(selected_regions)
            
            for i, region_name in enumerate(selected_regions):
                if progress_callback:
                    progress = 20 + int((i / total_regions) * 40)
                    progress_callback(progress, f"正在处理区域 {region_name}...")
                    
                if region_name in flow_data:
                    region_data = flow_data[region_name]
                    
                    # 创建VTK网格
                    vtk_grid = self.create_vtk_grid(region_data, self.sampling_rate)
                    if vtk_grid:
                        self.region_vtk_grids[region_name] = vtk_grid
                        print(f"✓ 区域 {region_name}: VTK网格创建完成")

            # 重置全局范围缓存，因为VTK网格已更新
            self._global_scalar_range = None

            return len(self.region_vtk_grids) > 0
            
        except Exception as e:
            print(f"创建VTK网格失败: {e}")
            return False

    def _create_all_core_actors(self, selected_regions: List[str],
                              progress_callback: Optional[Callable] = None,
                              data_source: str = "steady",
                              use_optimized_streamlines: bool = True):
        """创建所有核心可视化actors（预创建模式）"""
        try:
            self.region_actors.clear()

            # 计算全局压力范围
            global_scalar_range = self.get_global_scalar_range()
            if global_scalar_range:
                print(f"使用全局压力范围: {global_scalar_range[0]:.2f} ~ {global_scalar_range[1]:.2f} Pa")

            total_regions = len(selected_regions)

            for i, region_name in enumerate(selected_regions):
                if progress_callback:
                    progress = 60 + int((i / total_regions) * 15)
                    progress_callback(progress, f"正在创建区域 {region_name} 的核心可视化...")

                if region_name in self.region_vtk_grids:
                    vtk_grid = self.region_vtk_grids[region_name]
                    region_actors = {}

                    # 创建所有核心可视化对象（无论是否显示）

                    # 获取区域数据用于面单元渲染
                    flow_data = self.data_manager.get_flow_data_regions(data_source)
                    region_data = flow_data.get(region_name) if flow_data else None

                    if region_data:
                        print(f"区域 {region_name}: 点数={len(region_data)}, 有面片={region_data.has_faces()}")
                        if region_data.has_faces():
                            print(f"区域 {region_name}: 面片数={len(region_data.faces)}")
                    else:
                        print(f"警告: 无法获取区域 {region_name} 的数据")

                    # 压力分布 - 优先使用面单元渲染
                    pressure_actor = None
                    if region_data and region_data.has_faces():
                        # 使用专门的压力面单元渲染方法
                        print(f"区域 {region_name}: 尝试创建面单元压力可视化")
                        pressure_actor = self.surface_field_visualizer.create_pressure_surface_actor(
                            region_data, global_scalar_range)

                    # 如果面单元渲染失败或没有面片数据，回退到传统点云渲染
                    if pressure_actor is None:
                        print(f"区域 {region_name}: 使用传统点云压力渲染")
                        pressure_actor = self.pressure_visualizer.create_pressure_surface_with_range(
                            vtk_grid, global_scalar_range)

                    if pressure_actor:
                        region_actors['pressure'] = pressure_actor
                        self.add_actor(pressure_actor)
                        pressure_actor.SetVisibility(False)  # 初始隐藏
                        print(f"✓ 区域 {region_name}: 压力actor已创建并添加到渲染器")
                    else:
                        print(f"⚠️ 区域 {region_name}: 压力actor创建失败")

                    # 压力轮廓
                    contour_actor = self.pressure_visualizer.create_pressure_contour_with_range(
                        vtk_grid, self.contour_count, self.contour_line_width, global_scalar_range)
                    if contour_actor:
                        region_actors['contour'] = contour_actor
                        self.add_actor(contour_actor)
                        contour_actor.SetVisibility(False)  # 初始隐藏

                    # 流线
                    print(f"区域 {region_name}: 尝试创建流线可视化...")
                    # 检查VTK网格中的矢量数据
                    vectors = vtk_grid.GetPointData().GetVectors()
                    if vectors:
                        vector_range = vectors.GetRange(-1)
                        print(f"区域 {region_name}: 矢量数据范围 {vector_range[0]:.6f} ~ {vector_range[1]:.6f}")
                    else:
                        print(f"区域 {region_name}: 没有矢量数据")

                    # 流线处理：优化流线只创建一次全局流线
                    if use_optimized_streamlines and i == 0:  # 只在第一个区域创建全局流线
                        # 获取全局速度范围
                        global_velocity_range = self._calculate_global_velocity_range()

                        # 使用新的优化流线方法（从入口到出口）
                        streamline_actor = self.streamline_visualizer.create_optimized_streamlines(
                            self.region_vtk_grids, selected_regions,
                            max_streamlines=self.streamline_density//3,
                            use_velocity_based_seeds=True,
                            global_velocity_range=global_velocity_range)

                        if streamline_actor:
                            # 将全局流线添加到第一个区域
                            region_actors['streamline'] = streamline_actor
                            self.add_actor(streamline_actor)
                            streamline_actor.SetVisibility(False)  # 初始隐藏
                            print(f"✓ 全局优化流线创建成功")
                        else:
                            print("❌ 全局优化流线创建失败，回退到传统方法")
                            # 回退到传统方法
                            streamline_actor = self.streamline_visualizer.create_streamlines(
                                vtk_grid, self.streamline_density, region_name)
                    elif not use_optimized_streamlines:
                        # 传统流线方法：为每个区域创建流线
                        streamline_actor = self.streamline_visualizer.create_streamlines(
                            vtk_grid, self.streamline_density, region_name)
                    else:
                        # 优化流线模式下，其他区域不创建流线
                        streamline_actor = None

                    if streamline_actor:
                        region_actors['streamline'] = streamline_actor
                        self.add_actor(streamline_actor)
                        streamline_actor.SetVisibility(False)  # 初始隐藏
                        print(f"✓ 区域 {region_name}: 流线actor创建成功")
                    else:
                        print(f"⚠️ 区域 {region_name}: 流线actor创建失败")

                    # 速度矢量（使用速度颜色映射）
                    vector_actor = self.streamline_visualizer.create_velocity_vectors(
                        vtk_grid, self.vector_scale, use_velocity_coloring=True)
                    if vector_actor:
                        region_actors['vector'] = vector_actor
                        self.add_actor(vector_actor)
                        vector_actor.SetVisibility(False)  # 初始隐藏

                    if region_actors:
                        self.region_actors[region_name] = region_actors
                        print(f"✓ 区域 {region_name}: 预创建了 {len(region_actors)} 个核心可视化对象")

            # 标记流体actors已创建
            self._flow_actors_created = len(self.region_actors) > 0

        except Exception as e:
            print(f"创建核心可视化actors失败: {e}")

    def _create_extended_field_actors(self, selected_regions: List[str],
                                    extended_fields: Dict[str, bool],
                                    progress_callback: Optional[Callable] = None):
        """创建扩展物理场actors（延迟创建模式）"""
        try:
            # 只创建需要显示的扩展物理场
            fields_to_create = [field for field, show in extended_fields.items() if show]
            if not fields_to_create:
                return

            total_work = len(selected_regions) * len(fields_to_create)
            work_done = 0

            for region_name in selected_regions:
                if region_name not in self.extended_field_actors:
                    self.extended_field_actors[region_name] = {}

                # 获取区域数据
                flow_data = self.data_manager.get_flow_data_regions("steady")  # 假设使用稳态数据
                if region_name not in flow_data:
                    continue

                region_data = flow_data[region_name]
                available_fields = region_data.get_available_physical_fields()

                for field_name in fields_to_create:
                    work_done += 1
                    if progress_callback:
                        progress = 75 + int((work_done / total_work) * 10)
                        progress_callback(progress, f"正在创建 {region_name} 的 {field_name} 可视化...")

                    if field_name in available_fields:
                        # 计算全局范围（如果需要）
                        global_range = self._calculate_global_field_range(field_name, selected_regions)

                        # 创建面单元可视化
                        field_actor = self.surface_field_visualizer.create_surface_field_actor(
                            region_data, field_name, global_range)

                        if field_actor:
                            self.extended_field_actors[region_name][field_name] = field_actor
                            self.add_actor(field_actor)
                            field_actor.SetVisibility(True)  # 立即显示
                            print(f"✓ 区域 {region_name}: 创建了 {field_name} 可视化")
                        else:
                            print(f"⚠️ 区域 {region_name}: {field_name} 可视化创建失败")
                    else:
                        print(f"⚠️ 区域 {region_name}: 没有 {field_name} 数据")

        except Exception as e:
            print(f"创建扩展物理场actors失败: {e}")

    def _create_mesh_actors(self, selected_regions: List[str]):
        """创建网格actors"""
        try:
            # 清理现有的mesh actors
            if self.mesh_actors:
                for actor in self.mesh_actors.values():
                    if actor:
                        self.remove_actor(actor)
            self.mesh_actors.clear()

            for region_name in selected_regions:
                # 使用新的统一方法获取网格数据
                mesh_region = self.data_manager.get_any_mesh_region(region_name)

                if mesh_region:
                    mesh_actor = self.mesh_visualizer.create_mesh_actor(mesh_region)
                    if mesh_actor:
                        self.mesh_actors[region_name] = mesh_actor
                        self.add_actor(mesh_actor)
                        print(f"✓ 区域 {region_name}: 网格actor已创建")
                    else:
                        print(f"⚠️ 区域 {region_name}: 网格actor创建失败")
                else:
                    print(f"⚠️ 区域 {region_name}: 没有网格数据")

            # 标记网格actors已创建
            self._mesh_actors_created = len(self.mesh_actors) > 0

        except Exception as e:
            print(f"创建网格actors失败: {e}")
            import traceback
            traceback.print_exc()
            
    def _update_region_actors_visibility(self, selected_regions: List[str],
                                       show_pressure: bool, show_contours: bool,
                                       show_streamlines: bool, show_vectors: bool):
        """更新区域actors可见性"""
        try:
            print(f"更新区域actors可见性: {len(self.region_actors)} 个区域")
            for region_name, actors_dict in self.region_actors.items():
                is_selected = region_name in selected_regions

                # 更新各类可视化的可见性
                if 'pressure' in actors_dict:
                    visibility = is_selected and show_pressure
                    actors_dict['pressure'].SetVisibility(visibility)
                    print(f"区域 {region_name} 压力可见性: {visibility}")
                if 'contour' in actors_dict:
                    visibility = is_selected and show_contours
                    actors_dict['contour'].SetVisibility(visibility)
                    print(f"区域 {region_name} 轮廓可见性: {visibility}")
                if 'streamline' in actors_dict:
                    visibility = is_selected and show_streamlines
                    actors_dict['streamline'].SetVisibility(visibility)
                    print(f"区域 {region_name} 流线可见性: {visibility}")
                if 'vector' in actors_dict:
                    visibility = is_selected and show_vectors
                    actors_dict['vector'].SetVisibility(visibility)
                    print(f"区域 {region_name} 矢量可见性: {visibility}")

        except Exception as e:
            print(f"更新区域actors可见性失败: {e}")
            
    def _update_mesh_visibility(self, selected_regions: List[str], show_mesh: bool):
        """更新网格可见性"""
        try:
            for region_name, mesh_actor in self.mesh_actors.items():
                is_visible = region_name in selected_regions and show_mesh
                mesh_actor.SetVisibility(is_visible)

        except Exception as e:
            print(f"更新网格可见性失败: {e}")

    def _calculate_global_field_range(self, field_name: str, selected_regions: List[str]) -> Optional[Tuple[float, float]]:
        """计算指定物理场在所有选中区域的全局范围"""
        try:
            flow_data = self.data_manager.get_flow_data_regions("steady")  # 假设使用稳态数据
            all_values = []

            for region_name in selected_regions:
                if region_name in flow_data:
                    region_data = flow_data[region_name]
                    field_data = region_data.get_physical_field_data(field_name)
                    if field_data is not None:
                        all_values.extend(field_data.flatten())

            if all_values:
                return (min(all_values), max(all_values))
            return None

        except Exception as e:
            print(f"计算 {field_name} 全局范围失败: {e}")
            return None

    def _update_all_visibility(self, selected_regions: List[str],
                             show_pressure: bool, show_contours: bool,
                             show_streamlines: bool, show_vectors: bool,
                             show_mesh: bool, extended_fields: Optional[Dict[str, bool]] = None):
        """更新所有可视化对象的可见性"""
        try:
            # 更新核心物理场可见性
            self._update_region_actors_visibility(selected_regions, show_pressure, show_contours,
                                                show_streamlines, show_vectors)

            # 更新扩展物理场可见性
            if extended_fields:
                self._update_extended_field_visibility(selected_regions, extended_fields)

            # 更新网格可见性
            self._update_mesh_visibility(selected_regions, show_mesh)

        except Exception as e:
            print(f"更新可见性失败: {e}")

    def _update_extended_field_visibility(self, selected_regions: List[str],
                                        extended_fields: Dict[str, bool]):
        """更新扩展物理场可见性"""
        try:
            for region_name, field_actors in self.extended_field_actors.items():
                is_selected = region_name in selected_regions

                for field_name, actor in field_actors.items():
                    should_show = is_selected and extended_fields.get(field_name, False)
                    actor.SetVisibility(should_show)

        except Exception as e:
            print(f"更新扩展物理场可见性失败: {e}")

    def _update_mesh_visibility_only(self, selected_regions: List[str]):
        """仅更新网格可见性（用于仅渲染网格模式）"""
        try:
            for region_name, mesh_actor in self.mesh_actors.items():
                is_visible = region_name in selected_regions
                mesh_actor.SetVisibility(is_visible)
                if is_visible:
                    print(f"✓ 区域 {region_name} 网格已显示")
                else:
                    print(f"○ 区域 {region_name} 网格已隐藏")

        except Exception as e:
            print(f"更新网格可见性失败: {e}")

    def render_mesh_only_smart(self, selected_regions: List[str], force_recreate: bool = False) -> bool:
        """
        智能的仅渲染网格方法

        Args:
            selected_regions: 选中的区域列表
            force_recreate: 是否强制重新创建

        Returns:
            bool: 是否成功
        """
        try:
            if not selected_regions:
                print("没有选择区域")
                return False

            # 检查是否需要重新创建网格actors
            need_recreate = force_recreate or self._need_recreate_mesh_actors(selected_regions)

            if need_recreate:
                print("需要重新创建网格actors...")
                # 清空现有可视化
                self.clear_visualization()

                # 重新创建网格actors
                self._create_mesh_actors(selected_regions)
                self._mesh_actors_created = True

                # 添加坐标轴
                bounds = self._calculate_mesh_bounds(selected_regions)
                if bounds:
                    axes = self.create_axes_actor(bounds)
                    self.add_actor(axes)

                # 重置相机
                self.reset_camera()
                print(f"网格actors重新创建完成: {len(self.mesh_actors)} 个区域")
            else:
                print("使用缓存的网格actors，仅更新可见性...")
                # 仅更新可见性（仅渲染网格模式）
                self._update_mesh_visibility_only(selected_regions)

            # 渲染
            self.render()

            # 更新状态
            self._last_selected_regions = selected_regions.copy()

            visible_count = sum(1 for region_name, actor in self.mesh_actors.items()
                              if actor and actor.GetVisibility())
            print(f"✓ 网格渲染完成: {visible_count}/{len(self.mesh_actors)} 个区域可见")
            return True

        except Exception as e:
            print(f"智能网格渲染失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _calculate_mesh_bounds(self, selected_regions: List[str]) -> Optional[Tuple[float, float, float, float, float, float]]:
        """计算选中区域的网格边界"""
        try:
            bounds = [float('inf'), float('-inf')] * 3
            has_bounds = False

            for region_name in selected_regions:
                # 使用统一方法获取网格数据
                mesh_region = self.data_manager.get_any_mesh_region(region_name)
                if mesh_region:
                    region_bounds = mesh_region.get_bounds()
                    if region_bounds:
                        bounds[0] = min(bounds[0], region_bounds[0])
                        bounds[1] = max(bounds[1], region_bounds[1])
                        bounds[2] = min(bounds[2], region_bounds[2])
                        bounds[3] = max(bounds[3], region_bounds[3])
                        bounds[4] = min(bounds[4], region_bounds[4])
                        bounds[5] = max(bounds[5], region_bounds[5])
                        has_bounds = True

            return tuple(bounds) if has_bounds and bounds[0] != float('inf') else None

        except Exception as e:
            print(f"计算网格边界失败: {e}")
            return None

    def _add_auxiliary_elements(self, show_color_bar: bool = True):
        """添加辅助元素（坐标轴、颜色条等）"""
        try:
            # 添加坐标轴
            bounds = self._calculate_combined_bounds()
            axes = self.create_axes_actor(bounds)
            self.add_actor(axes)

            # 添加颜色条
            if show_color_bar and self.region_vtk_grids:
                # 先清除所有现有的2D actors（包括旧的颜色条）
                self._clear_2d_actors()

                # 检查当前显示的可视化类型
                has_visible_pressure = self._has_visible_pressure_actors()
                has_visible_optimized_streamlines = self._has_visible_optimized_streamlines()
                has_visible_velocity_vectors = self._has_visible_velocity_vectors()

                print(f"颜色条检测: 压力可见={has_visible_pressure}, 优化流线可见={has_visible_optimized_streamlines}, 速度矢量可见={has_visible_velocity_vectors}")

                # 同时显示压力和速度颜色条（如果都有可见的对象）
                color_bars_added = 0

                # 添加压力颜色条（仅当压力分布可见时）
                if has_visible_pressure:
                    print("显示压力颜色条")
                    global_scalar_range = self.get_global_scalar_range()
                    if global_scalar_range:
                        lut = self.create_rainbow_colormap()
                        lut.SetRange(global_scalar_range)
                        # 调整压力颜色条位置（左侧）
                        scalar_bar = self.create_scalar_bar(lut, "压力 [Pa]", position=(0.05, 0.1))
                        self.add_actor_2d(scalar_bar)
                        color_bars_added += 1
                        print(f"压力颜色条使用全局范围: {global_scalar_range[0]:.2f} ~ {global_scalar_range[1]:.2f} Pa")

                # 添加速度颜色条（仅当流线或速度矢量可见时）
                if has_visible_optimized_streamlines or has_visible_velocity_vectors:
                    print("显示速度颜色条")
                    # 根据是否已有压力颜色条调整位置
                    position = (0.85, 0.1) if color_bars_added == 0 else (0.85, 0.1)  # 右侧
                    self._add_velocity_color_bar(position=position)
                    color_bars_added += 1

                if color_bars_added == 0:
                    print("没有可显示的颜色条")

        except Exception as e:
            print(f"添加辅助元素失败: {e}")

    def _update_color_bars(self):
        """更新颜色条显示（根据当前可见性状态）"""
        try:
            # 先清除所有现有的2D actors（包括旧的颜色条）
            self._clear_2d_actors()

            # 检查当前显示的可视化类型
            has_visible_pressure = self._has_visible_pressure_actors()
            has_visible_optimized_streamlines = self._has_visible_optimized_streamlines()
            has_visible_velocity_vectors = self._has_visible_velocity_vectors()

            print(f"颜色条更新检测: 压力可见={has_visible_pressure}, 优化流线可见={has_visible_optimized_streamlines}, 速度矢量可见={has_visible_velocity_vectors}")

            # 同时显示压力和速度颜色条（如果都有可见的对象）
            color_bars_added = 0

            # 添加压力颜色条（仅当压力分布可见时）
            if has_visible_pressure:
                print("更新显示压力颜色条")
                global_scalar_range = self.get_global_scalar_range()
                if global_scalar_range:
                    lut = self.create_rainbow_colormap()
                    lut.SetRange(global_scalar_range)
                    # 调整压力颜色条位置（左侧）
                    scalar_bar = self.create_scalar_bar(lut, "压力 [Pa]", position=(0.05, 0.1))
                    self.add_actor_2d(scalar_bar)
                    color_bars_added += 1
                    print(f"压力颜色条使用全局范围: {global_scalar_range[0]:.2f} ~ {global_scalar_range[1]:.2f} Pa")

            # 添加速度颜色条（仅当流线或速度矢量可见时）
            if has_visible_optimized_streamlines or has_visible_velocity_vectors:
                print("更新显示速度颜色条")
                # 根据是否已有压力颜色条调整位置
                position = (0.85, 0.1) if color_bars_added == 0 else (0.85, 0.1)  # 右侧
                self._add_velocity_color_bar(position=position)
                color_bars_added += 1

            if color_bars_added == 0:
                print("没有可显示的颜色条")

        except Exception as e:
            print(f"更新颜色条失败: {e}")

    def _clear_2d_actors(self):
        """清除所有2D actors（如颜色条）"""
        try:
            if self.renderer:
                # 获取所有2D actors
                actors_2d = self.renderer.GetActors2D()
                actors_2d.InitTraversal()
                actors_to_remove = []

                # 收集需要移除的2D actors
                actor = actors_2d.GetNextItem()
                while actor:
                    actors_to_remove.append(actor)
                    actor = actors_2d.GetNextItem()

                # 移除所有2D actors
                for actor in actors_to_remove:
                    self.renderer.RemoveActor2D(actor)

                print(f"清除了 {len(actors_to_remove)} 个2D actors")
        except Exception as e:
            print(f"清除2D actors失败: {e}")

    def _has_visible_pressure_actors(self) -> bool:
        """检查是否有可见的压力actors"""
        try:
            for region_actors in self.region_actors.values():
                if 'pressure' in region_actors:
                    pressure_actor = region_actors['pressure']
                    if pressure_actor and pressure_actor.GetVisibility():
                        return True
                if 'contour' in region_actors:
                    contour_actor = region_actors['contour']
                    if contour_actor and contour_actor.GetVisibility():
                        return True
            return False
        except Exception as e:
            print(f"检查可见压力actors失败: {e}")
            return False

    def _has_visible_optimized_streamlines(self) -> bool:
        """检查是否有可见的优化流线（带速度颜色映射）"""
        try:
            for region_actors in self.region_actors.values():
                if 'streamline' in region_actors:
                    streamline_actor = region_actors['streamline']
                    if (streamline_actor and streamline_actor.GetVisibility() and
                        streamline_actor.GetMapper()):
                        mapper = streamline_actor.GetMapper()
                        # 检查是否有速度大小的标量数据
                        if (mapper.GetInput() and
                            mapper.GetInput().GetPointData() and
                            mapper.GetInput().GetPointData().GetArray("VelocityMagnitude")):
                            return True
            return False
        except Exception as e:
            print(f"检查可见优化流线失败: {e}")
            return False

    def _has_visible_velocity_vectors(self) -> bool:
        """检查是否有可见的速度矢量"""
        try:
            for region_actors in self.region_actors.values():
                if 'vector' in region_actors:
                    vector_actor = region_actors['vector']
                    if vector_actor and vector_actor.GetVisibility():
                        return True
            return False
        except Exception as e:
            print(f"检查可见速度矢量失败: {e}")
            return False

    def _add_velocity_color_bar(self, position: Tuple[float, float] = (0.85, 0.1)):
        """为流线添加速度颜色条"""
        try:
            # 首先尝试使用streamline_visualizer的颜色条方法
            if self.streamline_visualizer.has_velocity_color_data():
                velocity_color_bar = self.streamline_visualizer.create_velocity_color_bar(position=position)
                if velocity_color_bar:
                    self.velocity_color_bar = velocity_color_bar
                    self.add_actor_2d(velocity_color_bar)
                    print("✓ 速度颜色条已添加（使用streamline_visualizer）")
                    return

            # 回退方法：查找第一个有速度数据的流线actor
            velocity_range = None
            velocity_lut = None

            for region_actors in self.region_actors.values():
                if 'streamline' in region_actors:
                    streamline_actor = region_actors['streamline']
                    if streamline_actor and streamline_actor.GetMapper():
                        mapper = streamline_actor.GetMapper()
                        if (mapper.GetInput() and
                            mapper.GetInput().GetPointData() and
                            mapper.GetInput().GetPointData().GetArray("VelocityMagnitude")):
                            # 获取速度范围和颜色映射
                            velocity_range = mapper.GetScalarRange()
                            velocity_lut = mapper.GetLookupTable()
                            break

            if velocity_range and velocity_lut:
                # 创建速度颜色条
                scalar_bar = self.create_scalar_bar(velocity_lut, "速度 [m/s]", position=position)
                self.velocity_color_bar = scalar_bar
                self.add_actor_2d(scalar_bar)
                print(f"✓ 速度颜色条已添加: {velocity_range[0]:.3f} ~ {velocity_range[1]:.3f} m/s")
            else:
                print("❌ 未找到速度数据，无法创建速度颜色条")

        except Exception as e:
            print(f"添加速度颜色条失败: {e}")
            
    def _calculate_combined_bounds(self) -> Optional[Tuple[float, float, float, float, float, float]]:
        """计算所有VTK网格的组合边界"""
        try:
            if not self.region_vtk_grids:
                return None
                
            bounds = [float('inf'), float('-inf')] * 3
            
            for vtk_grid in self.region_vtk_grids.values():
                grid_bounds = vtk_grid.GetBounds()
                bounds[0] = min(bounds[0], grid_bounds[0])
                bounds[1] = max(bounds[1], grid_bounds[1])
                bounds[2] = min(bounds[2], grid_bounds[2])
                bounds[3] = max(bounds[3], grid_bounds[3])
                bounds[4] = min(bounds[4], grid_bounds[4])
                bounds[5] = max(bounds[5], grid_bounds[5])
                
            return tuple(bounds) if bounds[0] != float('inf') else None
            
        except Exception as e:
            print(f"计算组合边界失败: {e}")
            return None
            
    def set_visualization_parameters(self, sampling_rate: int = 1, streamline_density: int = 50,
                                   vector_scale: float = 1.0, contour_count: int = 15,
                                   contour_line_width: int = 2):
        """设置可视化参数"""
        self.sampling_rate = sampling_rate
        self.streamline_density = streamline_density
        self.vector_scale = vector_scale
        self.contour_count = contour_count
        self.contour_line_width = contour_line_width

    def create_3d_streamlines_from_inlets(self, data_source: str = "steady",
                                        max_points: int = 25,
                                        sampling_method: str = "vertex") -> bool:
        """
        创建从入口开始的3D流线（参考CFD软件实现）

        Args:
            data_source: 数据源 ("steady" 或 "transient")
            max_points: 最大点数限制
            sampling_method: 采样方法 ('vertex', 'uniform', 'random')

        Returns:
            bool: 是否创建成功
        """
        try:
            print("=== 创建3D入口流线 ===")

            # 获取预处理的VTK网格
            prepared_grids = self.data_manager.get_prepared_vtk_grids(data_source)
            if not prepared_grids:
                print("❌ 没有预处理的VTK网格数据")
                return False

            print(f"✓ 获取到 {len(prepared_grids)} 个预处理网格")

            # 使用简化的3D流线方法
            inlet_names = ['inlet1too', 'inlet2too', 'inlet3too', 'inlet4too']
            streamline_actor = self.streamline_visualizer.create_3d_streamlines_from_inlets(
                prepared_grids, inlet_names, max_points, sampling_method)

            if streamline_actor:
                # 移除旧的流线（如果存在）
                if hasattr(self, '_3d_streamline_actor') and self._3d_streamline_actor:
                    self.remove_actor(self._3d_streamline_actor)

                # 添加新的3D流线
                self._3d_streamline_actor = streamline_actor
                self.add_actor(streamline_actor)

                print("✓ 3D入口流线创建成功")
                return True
            else:
                print("❌ 3D入口流线创建失败")
                return False

        except Exception as e:
            print(f"创建3D入口流线失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    def get_visualization_info(self) -> Dict[str, Any]:
        """获取可视化信息"""
        return {
            'region_vtk_grids': len(self.region_vtk_grids),
            'region_actors': len(self.region_actors),
            'mesh_actors': len(self.mesh_actors),
            'total_actors': sum(len(actors) for actors in self.region_actors.values()) + len(self.mesh_actors),
            'mesh_actors_created': self._mesh_actors_created,
            'flow_actors_created': self._flow_actors_created
        }

    def update_region_selection_smart(self, selected_regions: List[str],
                                    show_pressure: bool = True, show_contours: bool = False,
                                    show_streamlines: bool = True, show_vectors: bool = False,
                                    show_mesh: bool = False) -> bool:
        """
        智能的区域选择更新方法

        Args:
            selected_regions: 选中的区域列表
            show_pressure: 是否显示压力分布
            show_contours: 是否显示压力轮廓
            show_streamlines: 是否显示流线
            show_vectors: 是否显示速度矢量
            show_mesh: 是否显示网格

        Returns:
            bool: 是否成功更新
        """
        try:
            updated = False

            # 更新流体可视化对象的可见性
            if self.region_actors:
                self._update_region_actors_visibility(selected_regions, show_pressure,
                                                    show_contours, show_streamlines, show_vectors)
                updated = True
                print(f"✓ 流体可视化可见性已更新: {len(selected_regions)} 个区域")

            # 更新网格可见性
            if self.mesh_actors:
                # 如果只有网格actors而没有流体actors，说明是仅渲染网格模式
                if not self.region_actors and self._mesh_actors_created:
                    # 仅渲染网格模式：根据选中区域显示网格
                    self._update_mesh_visibility_only(selected_regions)
                else:
                    # 混合模式：根据show_mesh参数控制
                    self._update_mesh_visibility(selected_regions, show_mesh)

                updated = True
                visible_mesh_count = sum(1 for region_name, actor in self.mesh_actors.items()
                                       if actor and actor.GetVisibility())
                print(f"✓ 网格可见性已更新: {visible_mesh_count} 个区域可见")

            if updated:
                # 重新渲染
                self.render()
                self._last_selected_regions = selected_regions.copy()
                return True
            else:
                print("没有可更新的可视化对象，请先创建可视化")
                return False

        except Exception as e:
            print(f"智能区域选择更新失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def has_mesh_actors(self) -> bool:
        """检查是否有网格actors"""
        return bool(self.mesh_actors and self._mesh_actors_created)

    def has_flow_actors(self) -> bool:
        """检查是否有流体actors"""
        return bool(self.region_actors and self._flow_actors_created)

    def clear_mesh_cache(self):
        """清空网格缓存"""
        self._mesh_data_hash = None
        self._mesh_actors_created = False

    def clear_flow_cache(self):
        """清空流体缓存"""
        self._flow_data_hash = None
        self._flow_actors_created = False
        self._global_scalar_range = None

    def toggle_extended_field(self, field_name: str, selected_regions: List[str],
                            show: bool, data_source: str = "steady") -> bool:
        """
        切换扩展物理场的显示状态（延迟创建）

        Args:
            field_name: 物理场名称
            selected_regions: 选中的区域列表
            show: 是否显示
            data_source: 数据源

        Returns:
            bool: 是否成功
        """
        try:
            if show:
                # 需要显示，检查是否已创建
                for region_name in selected_regions:
                    if (region_name not in self.extended_field_actors or
                        field_name not in self.extended_field_actors[region_name]):
                        # 需要创建
                        self._create_single_extended_field(region_name, field_name, data_source)

                    # 设置可见性
                    if (region_name in self.extended_field_actors and
                        field_name in self.extended_field_actors[region_name]):
                        self.extended_field_actors[region_name][field_name].SetVisibility(True)
            else:
                # 隐藏
                for region_name in selected_regions:
                    if (region_name in self.extended_field_actors and
                        field_name in self.extended_field_actors[region_name]):
                        self.extended_field_actors[region_name][field_name].SetVisibility(False)

            self.render()
            return True

        except Exception as e:
            print(f"切换扩展物理场 {field_name} 失败: {e}")
            return False

    def _create_single_extended_field(self, region_name: str, field_name: str, data_source: str):
        """为单个区域创建单个扩展物理场"""
        try:
            # 获取区域数据
            flow_data = self.data_manager.get_flow_data_regions(data_source)
            if region_name not in flow_data:
                print(f"区域 {region_name} 没有 {data_source} 数据")
                return

            region_data = flow_data[region_name]
            available_fields = region_data.get_available_physical_fields()

            if field_name not in available_fields:
                print(f"区域 {region_name} 没有 {field_name} 数据")
                return

            # 计算全局范围
            global_range = self._calculate_global_field_range(field_name, [region_name])

            # 创建可视化
            field_actor = self.surface_field_visualizer.create_surface_field_actor(
                region_data, field_name, global_range)

            if field_actor:
                if region_name not in self.extended_field_actors:
                    self.extended_field_actors[region_name] = {}
                self.extended_field_actors[region_name][field_name] = field_actor
                self.add_actor(field_actor)
                print(f"✓ 区域 {region_name}: 创建了 {field_name} 可视化")
            else:
                print(f"⚠️ 区域 {region_name}: {field_name} 可视化创建失败")

        except Exception as e:
            print(f"创建单个扩展物理场失败: {e}")

    def get_available_extended_fields(self, data_source: str = "steady") -> Dict[str, List[str]]:
        """
        获取所有区域可用的扩展物理场

        Args:
            data_source: 数据源

        Returns:
            Dict[str, List[str]]: {region_name: [field_names]}
        """
        try:
            flow_data = self.data_manager.get_flow_data_regions(data_source)
            result = {}

            for region_name, region_data in flow_data.items():
                available_fields = region_data.get_available_physical_fields()
                # 过滤出扩展物理场
                extended_fields = [field for field in available_fields.keys()
                                 if field in self.extended_fields]
                result[region_name] = extended_fields

            return result

        except Exception as e:
            print(f"获取可用扩展物理场失败: {e}")
            return {}

    def clear_extended_field_cache(self):
        """清空扩展物理场缓存"""
        try:
            # 移除所有扩展物理场actors
            for region_actors in self.extended_field_actors.values():
                for actor in region_actors.values():
                    if actor:
                        self.remove_actor(actor)

            self.extended_field_actors.clear()
            print("扩展物理场缓存已清空")

        except Exception as e:
            print(f"清空扩展物理场缓存失败: {e}")

    def _calculate_global_scalar_range(self) -> Optional[Tuple[float, float]]:
        """计算所有区域的全局压力范围"""
        try:
            if not self.region_vtk_grids:
                return None

            min_val = float('inf')
            max_val = float('-inf')
            has_scalars = False

            for region_name, vtk_grid in self.region_vtk_grids.items():
                scalars = vtk_grid.GetPointData().GetScalars()
                if scalars:
                    scalar_range = scalars.GetRange()
                    min_val = min(min_val, scalar_range[0])
                    max_val = max(max_val, scalar_range[1])
                    has_scalars = True
                    print(f"区域 {region_name} 压力范围: {scalar_range[0]:.2f} ~ {scalar_range[1]:.2f} Pa")

            if has_scalars and min_val != float('inf'):
                global_range = (min_val, max_val)
                print(f"全局压力范围: {min_val:.2f} ~ {max_val:.2f} Pa")
                return global_range

            return None

        except Exception as e:
            print(f"计算全局压力范围失败: {e}")
            return None

    def _calculate_global_velocity_range(self) -> Optional[Tuple[float, float]]:
        """计算所有区域的全局速度范围"""
        try:
            if not self.region_vtk_grids:
                return None

            min_vel = float('inf')
            max_vel = float('-inf')
            has_vectors = False

            for region_name, vtk_grid in self.region_vtk_grids.items():
                vectors = vtk_grid.GetPointData().GetVectors()
                if vectors:
                    vector_range = vectors.GetRange(-1)  # 获取矢量幅度范围
                    min_vel = min(min_vel, vector_range[0])
                    max_vel = max(max_vel, vector_range[1])
                    has_vectors = True
                    print(f"区域 {region_name} 速度范围: {vector_range[0]:.6f} ~ {vector_range[1]:.6f} m/s")

            if has_vectors and min_vel != float('inf'):
                global_range = (min_vel, max_vel)
                print(f"全局速度范围: {min_vel:.6f} ~ {max_vel:.6f} m/s")
                return global_range

            return None

        except Exception as e:
            print(f"计算全局速度范围失败: {e}")
            return None

    def clear_streamline_actors(self):
        """清除所有流线actors"""
        try:
            actors_to_remove = []

            # 遍历所有区域的actors
            for region_name, region_actors in self.region_actors.items():
                if 'streamline' in region_actors:
                    streamline_actor = region_actors['streamline']
                    if streamline_actor:
                        # 从渲染器中移除
                        self.remove_actor(streamline_actor)
                        actors_to_remove.append((region_name, 'streamline'))

            # 清除记录
            for region_name, actor_type in actors_to_remove:
                if region_name in self.region_actors:
                    self.region_actors[region_name].pop(actor_type, None)

            # 清除速度颜色条
            if self.velocity_color_bar:
                self.remove_actor_2d(self.velocity_color_bar)
                self.velocity_color_bar = None
                print("✓ 速度颜色条已清除")

            print(f"✓ 清除了 {len(actors_to_remove)} 个流线actors")

        except Exception as e:
            print(f"清除流线actors失败: {e}")

    def recreate_streamlines(self, selected_regions: List[str],
                           use_optimized_streamlines: bool = False) -> bool:
        """重新创建流线actors"""
        try:
            if not selected_regions:
                print("没有选择区域")
                return False

            # 清除现有流线
            self.clear_streamline_actors()

            # 重新创建流线
            for i, region_name in enumerate(selected_regions):
                if region_name in self.region_vtk_grids:
                    vtk_grid = self.region_vtk_grids[region_name]

                    # 确保region_actors存在
                    if region_name not in self.region_actors:
                        self.region_actors[region_name] = {}

                    region_actors = self.region_actors[region_name]

                    # 流线处理：优化流线只创建一次全局流线
                    if use_optimized_streamlines and i == 0:  # 只在第一个区域创建全局流线
                        # 获取全局速度范围
                        global_velocity_range = self._calculate_global_velocity_range()

                        # 使用新的优化流线方法（从入口到出口）
                        streamline_actor = self.streamline_visualizer.create_optimized_streamlines(
                            self.region_vtk_grids, selected_regions,
                            max_streamlines=self.streamline_density//3,
                            use_velocity_based_seeds=True,
                            global_velocity_range=global_velocity_range)

                        if streamline_actor:
                            # 将全局流线添加到第一个区域
                            region_actors['streamline'] = streamline_actor
                            self.add_actor(streamline_actor)
                            streamline_actor.SetVisibility(True)  # 设置为可见
                            print(f"✓ 全局优化流线创建成功")
                        else:
                            print("❌ 全局优化流线创建失败，回退到传统方法")
                            # 回退到传统方法
                            streamline_actor = self.streamline_visualizer.create_streamlines(
                                vtk_grid, self.streamline_density, region_name)
                            if streamline_actor:
                                region_actors['streamline'] = streamline_actor
                                self.add_actor(streamline_actor)
                                streamline_actor.SetVisibility(True)
                    elif not use_optimized_streamlines:
                        # 传统流线方法：为每个区域创建流线
                        streamline_actor = self.streamline_visualizer.create_streamlines(
                            vtk_grid, self.streamline_density, region_name)
                        if streamline_actor:
                            region_actors['streamline'] = streamline_actor
                            self.add_actor(streamline_actor)
                            streamline_actor.SetVisibility(True)

            # 重新渲染
            self.render()
            print(f"✓ 流线重新创建完成: {len(selected_regions)} 个区域")
            return True

        except Exception as e:
            print(f"重新创建流线失败: {e}")
            return False

    def get_global_scalar_range(self) -> Optional[Tuple[float, float]]:
        """获取全局压力范围（缓存版本）"""
        if self._global_scalar_range is None:
            self._global_scalar_range = self._calculate_global_scalar_range()
        return self._global_scalar_range

    def set_actor_opacity(self, actor_type: str, opacity: float):
        """
        设置指定类型actor的透明度

        Args:
            actor_type: actor类型 ('pressure', 'mesh', 'surface', 'contour', 'streamline', 'vector')
            opacity: 透明度值 (0.0-1.0)
        """
        try:
            opacity = max(0.0, min(1.0, opacity))  # 确保在有效范围内

            if actor_type == 'pressure':
                # 设置压力可视化透明度
                for region_actors in self.region_actors.values():
                    if 'pressure' in region_actors:
                        region_actors['pressure'].GetProperty().SetOpacity(opacity)

            elif actor_type == 'mesh':
                # 设置网格透明度
                for mesh_actor in self.mesh_actors.values():
                    if mesh_actor:
                        mesh_actor.GetProperty().SetOpacity(opacity)

            elif actor_type == 'surface':
                # 设置面单元透明度
                for region_actors in self.extended_field_actors.values():
                    for field_actor in region_actors.values():
                        if field_actor:
                            field_actor.GetProperty().SetOpacity(opacity)

            elif actor_type == 'contour':
                # 设置轮廓线透明度
                for region_actors in self.region_actors.values():
                    if 'contour' in region_actors:
                        region_actors['contour'].GetProperty().SetOpacity(opacity)

            elif actor_type == 'streamline':
                # 设置流线透明度
                for region_actors in self.region_actors.values():
                    if 'streamline' in region_actors:
                        region_actors['streamline'].GetProperty().SetOpacity(opacity)

            elif actor_type == 'vector':
                # 设置矢量透明度
                for region_actors in self.region_actors.values():
                    if 'vector' in region_actors:
                        region_actors['vector'].GetProperty().SetOpacity(opacity)

            # 重新渲染
            self.render()
            print(f"✓ {actor_type} 透明度已设置为 {opacity:.2f}")

        except Exception as e:
            print(f"设置 {actor_type} 透明度失败: {e}")

    def set_all_opacity(self, opacity_params: Dict[str, float]):
        """
        批量设置所有actor的透明度

        Args:
            opacity_params: 透明度参数字典
        """
        try:
            if 'pressure_opacity' in opacity_params:
                self.set_actor_opacity('pressure', opacity_params['pressure_opacity'])

            if 'mesh_opacity' in opacity_params:
                self.set_actor_opacity('mesh', opacity_params['mesh_opacity'])

            if 'surface_opacity' in opacity_params:
                self.set_actor_opacity('surface', opacity_params['surface_opacity'])

            print("✓ 所有透明度设置完成")

        except Exception as e:
            print(f"批量设置透明度失败: {e}")

    def get_current_opacity(self, actor_type: str) -> Optional[float]:
        """
        获取指定类型actor的当前透明度

        Args:
            actor_type: actor类型

        Returns:
            float: 透明度值，如果没有找到则返回None
        """
        try:
            if actor_type == 'pressure':
                for region_actors in self.region_actors.values():
                    if 'pressure' in region_actors:
                        return region_actors['pressure'].GetProperty().GetOpacity()

            elif actor_type == 'mesh':
                for mesh_actor in self.mesh_actors.values():
                    if mesh_actor:
                        return mesh_actor.GetProperty().GetOpacity()

            elif actor_type == 'surface':
                for region_actors in self.extended_field_actors.values():
                    for field_actor in region_actors.values():
                        if field_actor:
                            return field_actor.GetProperty().GetOpacity()

            return None

        except Exception as e:
            print(f"获取 {actor_type} 透明度失败: {e}")
            return None
